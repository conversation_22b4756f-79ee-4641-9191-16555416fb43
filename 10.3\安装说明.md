# 道路标线CAD插件安装说明

## 项目概述
这是一个AutoCAD道路标线生成插件，可以自动生成道路标线、停止线、斑马线等交通标线。

## 编译要求
- Visual Studio 2019或更高版本（或MSBuild工具）
- .NET Framework 4.8
- AutoCAD 2020或更高版本（用于引用DLL文件）

## 编译步骤

### 方法一：使用批处理文件（推荐）
1. 双击运行 `build.bat` 文件
2. 脚本会自动检测编译环境并编译项目
3. 编译成功后，DLL文件将生成在 `bin\Release\` 目录下

### 方法二：使用Visual Studio
1. 打开 `10.3.sln` 解决方案文件
2. 在Visual Studio中选择 "Release" 配置
3. 右键点击项目，选择"生成"
4. 编译完成后，在 `bin\Release\` 目录下找到 `RoadMarkingPlugin.dll`

### 方法三：使用命令行
```bash
# 使用MSBuild
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" RoadMarkingPlugin.csproj /p:Configuration=Release

# 或使用.NET CLI
dotnet build RoadMarkingPlugin.csproj --configuration Release
```

## AutoCAD引用配置
在编译前，请确保项目文件中的AutoCAD引用路径正确：

```xml
<!-- 根据您的AutoCAD版本调整路径 -->
<Reference Include="AcCoreMgd">
  <HintPath>C:\Program Files\Autodesk\AutoCAD 2024\AcCoreMgd.dll</HintPath>
  <Private>False</Private>
</Reference>
```

常见AutoCAD版本路径：
- AutoCAD 2024: `C:\Program Files\Autodesk\AutoCAD 2024\`
- AutoCAD 2023: `C:\Program Files\Autodesk\AutoCAD 2023\`
- AutoCAD 2022: `C:\Program Files\Autodesk\AutoCAD 2022\`

## 插件安装

### 1. 复制DLL文件
将编译生成的 `RoadMarkingPlugin.dll` 复制到以下位置之一：
- AutoCAD安装目录的插件文件夹
- 或任意您选择的文件夹

### 2. 在AutoCAD中加载插件
1. 打开AutoCAD
2. 在命令行输入 `NETLOAD` 并按回车
3. 在弹出的对话框中选择 `RoadMarkingPlugin.dll` 文件
4. 点击"打开"加载插件

### 3. 使用插件
加载成功后，可以使用以下命令：
- `ROADMARKING` - 打开道路标线工具主界面
- `ROADMARKING_INFO` - 显示插件信息
- `ROADMARKING_INIT` - 初始化插件

## 功能说明

### 主要功能
- 自动生成道路中心线
- 生成车道边界线
- 生成道路边线
- 创建虚线标记
- 生成停止线（待完善）
- 生成斑马线（待完善）

### 参数设置
插件界面包含以下可调参数：
- 中心双黄线间距
- 单车道宽度
- 单黄线宽度
- 同向车道数量
- 各种线条宽度
- 虚线长度和间隔
- 斑马线参数

### 使用流程
1. 在AutoCAD中绘制道路中心基准线（直线、多段线、圆弧或样条曲线）
2. 运行 `ROADMARKING` 命令打开插件界面
3. 设置各项参数
4. 点击"生成道路标线"按钮
5. 按提示选择基准线
6. 插件将自动生成道路标线

## 故障排除

### 编译错误
1. **找不到AutoCAD引用**：检查项目文件中的AutoCAD DLL路径是否正确
2. **.NET版本不匹配**：确保项目目标框架为.NET Framework 4.8
3. **缺少依赖项**：确保安装了Visual Studio或MSBuild工具

### 运行时错误
1. **插件加载失败**：检查AutoCAD版本是否与编译时的引用版本兼容
2. **命令不识别**：确保插件已正确加载，可以使用 `NETLOAD` 重新加载
3. **功能异常**：检查AutoCAD命令行输出的错误信息

## 开发说明

### 项目结构
```
├── RoadMarkingForm.cs          # 主窗体逻辑
├── RoadMarkingForm.Designer.cs # 窗体设计器文件
├── RoadMarkingForm.resx        # 窗体资源文件
├── PluginCommands.cs           # AutoCAD命令定义
├── Properties/
│   └── AssemblyInfo.cs         # 程序集信息
├── RoadMarkingPlugin.csproj    # 项目文件
└── 10.3.sln                   # 解决方案文件
```

### 扩展开发
如需添加新功能，可以：
1. 在 `RoadMarkingForm.cs` 中添加新的生成方法
2. 在 `PluginCommands.cs` 中添加新的AutoCAD命令
3. 更新窗体界面以添加新的参数控件

## 版本信息
- 版本：1.0
- 开发环境：Visual Studio 2022
- 目标框架：.NET Framework 4.8
- 兼容AutoCAD：2020及以上版本

## 联系方式
如有问题或建议，请联系开发者。
